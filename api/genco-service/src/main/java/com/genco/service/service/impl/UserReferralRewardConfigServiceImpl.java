package com.genco.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.genco.common.exception.CrmebException;
import com.genco.common.model.system.SystemUserLevel;
import com.genco.common.model.user.User;
import com.genco.common.model.user.UserReferralRewardConfig;
import com.genco.common.response.PlatformRewardRulesResponse;
import com.genco.common.response.UserReferralStatsResponse;
import com.genco.common.token.FrontTokenComponent;
import com.genco.service.dao.UserReferralRewardConfigDao;
import com.genco.service.service.SystemConfigService;
import com.genco.service.service.SystemUserLevelService;
import com.genco.service.service.UserReferralRewardConfigService;
import com.genco.service.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;

/**
 * 用户拉新奖励配置服务实现类
 */
@Slf4j
@Service
public class UserReferralRewardConfigServiceImpl extends ServiceImpl<UserReferralRewardConfigDao, UserReferralRewardConfig> implements UserReferralRewardConfigService {

    @Autowired
    private UserReferralRewardConfigDao userReferralRewardConfigDao;

    @Autowired
    private UserService userService;

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private FrontTokenComponent tokenComponent;

    /**
     * 获取当前用户的拉新奖励配置
     * 优先级：银级会员配置 > 平台级配置
     */
    @Override
    public UserReferralRewardConfig getCurrentUserReferralRewardConfig() {
        Integer uid = tokenComponent.getUserId();
        if (uid == null) {
            throw new CrmebException("登录信息已过期，请重新登录！");
        }
        return getUserReferralRewardConfig(uid);
    }

    /**
     * 获取银级会员为下级设置的拉新奖励配置
     */
    @Override
    public UserReferralRewardConfig getMyReferralRewardConfig() {
        Integer currentUid = tokenComponent.getUserId();
        if (currentUid == null) {
            throw new CrmebException("登录信息已过期，请重新登录！");
        }

        // 验证权限
        if (!checkConfigPermission(currentUid)) {
            throw new CrmebException("只有银级会员才能查看自己的配置");
        }

        // 查找当前银级会员的配置
        LambdaQueryWrapper<UserReferralRewardConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserReferralRewardConfig::getUid, currentUid);
        UserReferralRewardConfig config = getOne(wrapper);

        if (config == null) {
            // 返回默认配置
            config = new UserReferralRewardConfig();
            config.setUid(currentUid);
            config.setReferralCount(5);
            config.setFirstOrderCount(3);
            config.setRewardAmount(new BigDecimal("25000"));
            config.setStatus(1);
        }

        return config;
    }

    /**
     * 保存或更新银级会员为下级设置的拉新奖励配置
     */
    @Override
    public Boolean saveOrUpdateMyConfig(UserReferralRewardConfig config) {
        Integer currentUid = tokenComponent.getUserId();
        if (currentUid == null) {
            throw new CrmebException("登录信息已过期，请重新登录！");
        }

        // 验证权限
        if (!checkConfigPermission(currentUid)) {
            throw new CrmebException("只有银级会员才能配置下级的拉新奖励");
        }

        // 设置用户ID
        config.setUid(currentUid);

        // 验证首单数量不能大于拉新数量
        if (config.getFirstOrderCount() > config.getReferralCount()) {
            throw new CrmebException("首单数量不能大于拉新数量");
        }

        return saveOrUpdateConfig(config);
    }

    /**
     * 验证当前用户是否有权限配置拉新奖励
     */
    @Override
    public Boolean checkCurrentUserConfigPermission() {
        Integer currentUid = tokenComponent.getUserId();
        if (currentUid == null) {
            return false;
        }
        return checkConfigPermission(currentUid);
    }

    /**
     * 获取用户的拉新奖励配置
     * 优先级：银级会员配置 > 平台级配置
     */
    @Override
    public UserReferralRewardConfig getUserReferralRewardConfig(Integer uid) {
        // 1. 查找用户信息
        User user = userService.getById(uid);
        if (user == null) {
            throw new CrmebException("用户不存在");
        }

        // 2. 查找用户的上级
        if (user.getSpreadUid() != null && user.getSpreadUid() > 0) {
            User parentUser = userService.getById(user.getSpreadUid());
            if (parentUser != null) {
                // 3. 判断上级是否是银级及以上会员
                SystemUserLevel parentLevel = systemUserLevelService.getByLevelId(parentUser.getLevel());
                if (parentLevel != null && parentLevel.getGrade() >= 2) {
                    // 4. 查找银级会员的拉新奖励配置
                    UserReferralRewardConfig parentConfig = userReferralRewardConfigDao.selectByUid(parentUser.getUid());
                    if (parentConfig != null && parentConfig.getStatus() == 1) {
                        return parentConfig;
                    }
                }
            }
        }

        // 5. 使用平台级配置
        return getPlatformReferralRewardConfig();
    }

    /**
     * 保存或更新用户的拉新奖励配置（私有方法）
     */
    private Boolean saveOrUpdateConfig(UserReferralRewardConfig config) {
        // 1. 验证权限
        if (!checkConfigPermission(config.getUid())) {
            throw new CrmebException("只有银级会员才能配置拉新奖励");
        }

        // 2. 查找是否已有配置
        LambdaQueryWrapper<UserReferralRewardConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserReferralRewardConfig::getUid, config.getUid());
        UserReferralRewardConfig existConfig = getOne(wrapper);

        if (existConfig != null) {
            // 更新
            config.setId(existConfig.getId());
            config.setUpdateTime(new Date());
            return updateById(config);
        } else {
            // 新增
            config.setCreateTime(new Date());
            config.setUpdateTime(new Date());
            return save(config);
        }
    }

    /**
     * 验证用户是否有权限配置拉新奖励
     */
    @Override
    public Boolean checkConfigPermission(Integer uid) {
        User user = userService.getById(uid);
        if (user == null) {
            return false;
        }

        // 检查是否是银级及以上会员
        SystemUserLevel userLevel = systemUserLevelService.getByLevelId(user.getLevel());
        return userLevel != null && userLevel.getGrade() >= 2;
    }

    /**
     * 获取平台级拉新奖励配置
     */
    private UserReferralRewardConfig getPlatformReferralRewardConfig() {
        // 从系统配置中获取平台级配置（formId=144）
        HashMap<String, String> platformConfig = systemConfigService.info(144);

        UserReferralRewardConfig config = new UserReferralRewardConfig();
        config.setUid(0); // 平台级配置用0表示
        config.setReferralCount(Integer.parseInt(platformConfig.getOrDefault("referral_count", "0")));
        config.setFirstOrderCount(Integer.parseInt(platformConfig.getOrDefault("first_order_count", "0")));
        config.setRewardAmount(new BigDecimal(platformConfig.getOrDefault("reward_amount", "0")));
        config.setStatus(1);

        return config;
    }

    /**
     * 获取平台级多语言奖励规则
     */
    @Override
    public PlatformRewardRulesResponse getPlatformRewardRules() {
        // 从系统配置中获取平台级多语言规则（formId=144）
        HashMap<String, String> platformConfig = systemConfigService.info(144);

        PlatformRewardRulesResponse response = new PlatformRewardRulesResponse();
        response.setRewardRuleZh(platformConfig.getOrDefault("reward_rule_zh", ""));
        response.setRewardRuleEn(platformConfig.getOrDefault("reward_rule_en", ""));
        response.setRewardRuleId(platformConfig.getOrDefault("reward_rule_id", ""));

        return response;
    }

    /**
     * 获取当前用户拉新统计数据
     */
    @Override
    public UserReferralStatsResponse getCurrentUserReferralStats() {
        Integer uid = tokenComponent.getUserId();
        if (uid == null) {
            throw new CrmebException("登录信息已过期，请重新登录！");
        }

        // 获取用户信息
        User user = userService.getById(uid);
        if (user == null) {
            throw new CrmebException("用户不存在");
        }

        UserReferralStatsResponse response = new UserReferralStatsResponse();

        // 获取用户余额
        response.setUserBalance(user.getNowMoney() != null ? user.getNowMoney() : BigDecimal.ZERO);

        // 获取累计邀请人数（下级用户数量）
        response.setTotalReferralCount(user.getSpreadCount() != null ? user.getSpreadCount() : 0);

        // 获取累计首单数（通过查询下级用户的首单情况）
        Integer totalFirstOrderCount = userReferralRewardConfigDao.getTotalFirstOrderCountBySpreadUid(uid);
        response.setTotalFirstOrderCount(totalFirstOrderCount != null ? totalFirstOrderCount : 0);

        // 获取已兑换邀请人数和已兑换首单数
        // 这里需要根据实际的奖励兑换记录来计算
        // 暂时返回0，后续可以根据具体的奖励兑换表来实现
        response.setRedeemedReferralCount(0);
        response.setRedeemedFirstOrderCount(0);

        return response;
    }
}
