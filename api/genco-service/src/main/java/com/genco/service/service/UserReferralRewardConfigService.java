package com.genco.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.genco.common.model.user.UserReferralRewardConfig;
import com.genco.common.response.PlatformRewardRulesResponse;
import com.genco.common.response.UserReferralStatsResponse;

/**
 * 用户拉新奖励配置服务接口
 */
public interface UserReferralRewardConfigService extends IService<UserReferralRewardConfig> {

    /**
     * 获取当前用户的拉新奖励配置
     * 优先级：银级会员配置 > 平台级配置
     *
     * @return UserReferralRewardConfig
     */
    UserReferralRewardConfig getCurrentUserReferralRewardConfig();

    /**
     * 获取银级会员为下级设置的拉新奖励配置
     * 只有银级会员才能查看
     *
     * @return UserReferralRewardConfig
     */
    UserReferralRewardConfig getMyReferralRewardConfig();

    /**
     * 保存或更新银级会员为下级设置的拉新奖励配置
     * 只有银级会员才能配置
     *
     * @param config 配置信息
     * @return Boolean
     */
    Boolean saveOrUpdateMyConfig(UserReferralRewardConfig config);

    /**
     * 验证当前用户是否有权限配置拉新奖励
     * 只有银级会员才能配置
     *
     * @return Boolean
     */
    Boolean checkCurrentUserConfigPermission();

    /**
     * 获取平台级多语言奖励规则
     *
     * @return PlatformRewardRulesResponse
     */
    PlatformRewardRulesResponse getPlatformRewardRules();

    /**
     * 获取当前用户拉新统计数据
     *
     * @return UserReferralStatsResponse
     */
    UserReferralStatsResponse getCurrentUserReferralStats();

    // 保留原有方法供内部使用
    /**
     * 获取用户的拉新奖励配置
     * 优先级：银级会员配置 > 平台级配置
     *
     * @param uid 用户ID
     * @return UserReferralRewardConfig
     */
    UserReferralRewardConfig getUserReferralRewardConfig(Integer uid);

    /**
     * 验证用户是否有权限配置拉新奖励
     * 只有银级会员才能配置
     *
     * @param uid 用户ID
     * @return Boolean
     */
    Boolean checkConfigPermission(Integer uid);
}
