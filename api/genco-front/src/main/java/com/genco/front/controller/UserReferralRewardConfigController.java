package com.genco.front.controller;

import com.genco.common.model.user.UserReferralRewardConfig;
import com.genco.common.request.UserReferralRewardConfigRequest;
import com.genco.common.response.CommonResult;
import com.genco.common.response.PlatformRewardRulesResponse;
import com.genco.common.response.UserReferralRewardConfigResponse;
import com.genco.common.response.UserReferralStatsResponse;
import com.genco.service.service.UserReferralRewardConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户拉新奖励配置控制器
 */
@Slf4j
@RestController
@RequestMapping("api/front/user/referral-reward")
@Api(tags = "用户拉新奖励配置")
public class UserReferralRewardConfigController {

    @Autowired
    private UserReferralRewardConfigService userReferralRewardConfigService;

    /**
     * 获取拉新奖励配置
     * 优先级：银级会员配置 > 平台级配置
     */
    @ApiOperation(value = "获取拉新奖励配置")
    @RequestMapping(value = "/config", method = RequestMethod.GET)
    public CommonResult<UserReferralRewardConfigResponse> getReferralRewardConfig() {
        // 获取拉新奖励配置
        UserReferralRewardConfig config = userReferralRewardConfigService.getCurrentUserReferralRewardConfig();

        // 转换为响应对象
        UserReferralRewardConfigResponse response = new UserReferralRewardConfigResponse();
        BeanUtils.copyProperties(config, response);

        // 设置配置来源和可编辑性
        if (config.getUid() == 0) {
            response.setConfigSource("PLATFORM");
        } else {
            response.setConfigSource("SILVER_MEMBER");
        }

        // 所有用户都不能编辑获取到的配置（因为这是给自己用的配置，不是自己设置的）
        response.setEditable(false);

        // 获取并设置平台级多语言奖励规则（通用信息）
        PlatformRewardRulesResponse platformRules = userReferralRewardConfigService.getPlatformRewardRules();
        response.setRewardRuleZh(platformRules.getRewardRuleZh());
        response.setRewardRuleEn(platformRules.getRewardRuleEn());
        response.setRewardRuleId(platformRules.getRewardRuleId());

        // 获取并设置用户拉新统计数据
        UserReferralStatsResponse userStats = userReferralRewardConfigService.getCurrentUserReferralStats();
        response.setUserBalance(userStats.getUserBalance());
        response.setTotalReferralCount(userStats.getTotalReferralCount());
        response.setRedeemedReferralCount(userStats.getRedeemedReferralCount());
        response.setTotalFirstOrderCount(userStats.getTotalFirstOrderCount());
        response.setRedeemedFirstOrderCount(userStats.getRedeemedFirstOrderCount());

        return CommonResult.success(response);
    }

    /**
     * 获取银级会员为下级设置的拉新奖励配置
     * 只有银级会员才能查看自己设置的配置
     */
    @ApiOperation(value = "获取银级会员为下级设置的拉新奖励配置")
    @RequestMapping(value = "/my-config", method = RequestMethod.GET)
    public CommonResult<UserReferralRewardConfigResponse> getMyReferralRewardConfig() {
        // 获取银级会员为下级设置的配置
        UserReferralRewardConfig config = userReferralRewardConfigService.getMyReferralRewardConfig();

        UserReferralRewardConfigResponse response = new UserReferralRewardConfigResponse();
        BeanUtils.copyProperties(config, response);

        if (config.getId() != null) {
            response.setConfigSource("MY_CONFIG");
        } else {
            response.setConfigSource("DEFAULT");
        }
        response.setEditable(true);

        return CommonResult.success(response);
    }

    /**
     * 编辑银级会员为下级设置的拉新奖励配置
     * 只有银级会员才能配置
     */
    @ApiOperation(value = "编辑银级会员为下级设置的拉新奖励配置")
    @RequestMapping(value = "/my-config", method = RequestMethod.POST)
    public CommonResult<Boolean> updateMyReferralRewardConfig(@RequestBody @Validated UserReferralRewardConfigRequest request) {
        // 转换为实体对象
        UserReferralRewardConfig config = new UserReferralRewardConfig();
        BeanUtils.copyProperties(request, config);

        // 保存配置
        Boolean result = userReferralRewardConfigService.saveOrUpdateMyConfig(config);

        if (result) {
            return CommonResult.success(true, "配置保存成功");
        } else {
            return CommonResult.failed("配置保存失败");
        }
    }

    /**
     * 检查当前用户是否有配置权限
     */
    @ApiOperation(value = "检查配置权限")
    @RequestMapping(value = "/check-permission", method = RequestMethod.GET)
    public CommonResult<Boolean> checkConfigPermission() {
        return CommonResult.success(userReferralRewardConfigService.checkCurrentUserConfigPermission());
    }
}
