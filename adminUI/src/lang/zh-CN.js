export default {
  common: {
    confirm: "确定",
    cancel: "取消",
    tip: "提示",
    cancelled: "已取消",
    deleteFile: "永久删除该文件",
    systemTip: "系统提示"
  },
  navbar: {
    home: "主页",
    profile: "个人中心",
    logout: "退出"
  },

  common: {
    editSuccess: "修改成功",
    addSuccess: "新增成功",
    confirmDelete: "是否确认删除名称为“{name}”的数据项？",
    status: "状态",
    fetchDataFailed: "获取数据失败",
    operationSuccess: "操作成功",
    operationFailed: "操作失败",
    unknownError: "未知错误",
    confirm: "确定",
    cancel: "取消",
    deleteConfirm: "确定删除？",
    deleteSuccess: "删除成功",
    deleteFailed: "删除失败",
    saveSuccess: "保存成功",
    saveFailed: "保存失败",
    enterRejectReason: "请输入拒绝原因",
    startDate: "开始日期",
    endDate: "结束日期",
    all: "全部",
    serialNumber: "序号",
    query: "查询",
    reset: "重置",
    enter: "请输入",
    pendingReview: "待审核",
    reviewedPassed: "已通过",
    reviewedRejected: "已拒绝",
    pleaseSelect: "请选择",
    yes: "是",
    no: "否",
    show: "显示",
    hide: "不显示",
    unknown: "未知",
    keyword: {
      text: "文字消息",
      image: "图片消息",
      news: "图文消息",
      voice: "声音消息"
    },
    couponType: {
      general: "通用券",
      product: "商品券",
      category: "品类券"
    },
    couponReceive: {
      manual: "手动领取",
      newUser: "新人券",
      gift: "赠送券"
    },
    paymentStatus: {
      unpaid: "未支付",
      paid: "已支付"
    },
    withdrawType: {
      bank: "银行卡",
      alipay: "支付宝",
      wechat: "微信"
    },
    rechargeType: {
      wechatPublic: "微信公众号",
      wechatH5: "微信H5支付",
      miniProgram: "小程序"
    },
    withdrawStatus: {
      rejected: "已拒绝",
      reviewing: "审核中",
      withdrawn: "已提现"
    },
    status: {
      bargain: {
        1: "进行中",
        2: "未完成",
        3: "已成功"
      }
    },
    onePass: {
      sms: "短信",
      copy: "商品采集",
      expr_query: "物流查询",
      expr_dump: "电子面单打印"
    },
    editStatus: {
      1: "未审核",
      2: "审核中",
      3: "审核失败",
      4: "审核成功"
    },
    videoStatus: {
      0: "初始值",
      5: "上架",
      11: "自主下架",
      13: "违规下架/风控系统下架"
    }
  },
  appMain: {
    copyright: "Copyright © 2025"
  },
  dashboard: {
    home: "首页",
    brandCenter: "品牌中心",
    brandManage: "品牌管理",
    productManage: "商品管理",
    appManage: "App管理",
    homeManage: "首页管理",
    opsCenter: "运营中心",
    affiliateProducts: "联盟选品",
    withdrawalReview: "提现审核",
    withdrawalRecords: "提现记录",
    orderCenter: "订单中心",
    orderInquiry: "订单查询",
    userCenter: "用户中心",
    userManage: "用户管理",
    userLevel: "用户等级",
    levelUpgradeOrder: "等级升级订单",
    financeCenter: "财务中心",
    financeDetails: "财务明细",
    withdrawalRequest: "提现申请",
    paramSettings: "参数设置",
    rewardRules: "奖励规则设置",
    referralRewardConfig: "拉新奖励配置",
    withdrawalFee: "提现手续费设置",
    membershipFee: "会员升级费设置",
    accountCenter: "账户中心",
    adminPermissions: "管理权限",
    roleManage: "角色管理",
    adminList: "管理员列表",
    permissionRules: "权限规则",
    profile: "个人中心",
    systemSettings: "系统设置",
    chainTransferRecord: "转链记录",
    platformCashbackRate: "平台返现率设置",
    shoppingCashbackRules: "购物返佣规则"
  },
  platformCashbackRate: {
    platformCashbackRate: "平台返现率",
    editTitle: "编辑平台返现率",
    addTitle: "新增平台返现率",
    placeholder: {
      platformCashbackRate: "请输入平台返现率"
    }
  },
  tagsView: {
    refresh: "刷新",
    close: "关闭",
    closeOthers: "关闭其他",
    closeAll: "关闭所有"
  },
  homepage: {
    welcome: "欢迎来到GENCO管理后台！",
    paymentSwitch: "支付开关",
    paymentSwitchTip1: "支付开关开启则前端允许设计为代理和合作伙伴，",
    paymentSwitchTip2: "开关关闭则不允许升级。此处只为方便AppStore提交审核，",
    paymentSwitchTip3: "请勿随便操作。",
    loginMode: "登录方式",
    loginModeTip1: "登录方式仅用于控制登录页TikTok登录和短信登录的",
    loginModeTip2: "显示和隐藏，方便AppStore提交审核，请勿随便操作。",
    tikTokLogin: "TikTok登录",
    smsLogin: "短信登录",
    submit: "确定"
  },
  brand: {
    search: "品牌搜索：",
    status: "状态：",
    pleaseSelect: "请选择",
    reset: "重置",
    query: "查询",
    addBrand: "新增品牌",
    batchOnline: "批量上架",
    batchOffline: "批量下架",
    batchDelete: "批量删除",
    brandLogo: "品牌Logo",
    brandName: "品牌名称",
    industry: "所属行业",
    platform: "入驻电商平台",
    productCount: "商品数量",
    maxCashback: "商品最高返现率",
    soldCount: "商品已售数量",
    soldAmount: "商品已售总金额（Rp）",
    cashbackAmount: "商品已返现金额（Rp）",
    shareCount: "品牌分享数量",
    createTime: "品牌创建时间",
    creator: "创建人员",
    statusLabel: "状态",
    isHot: "是否是热卖品牌",
    isHighCashback: "是否是高返现品牌",
    offline: "下架",
    online: "上架",
    edit: "编辑",
    delete: "删除",
    addDialogTitle: "新增品牌",
    brandNameInput: "请输入品牌名称",
    brandLogoInput: "请输入图片地址",
    contactPerson: "联系人：",
    contactPhone: "联系电话：",
    isOnline: "是否上架：",
    confirm: "确定",
    cancel: "取消",
    platformTiktok: "TikTok",
    platformShopee: "Shopee",
    confirmOperation: "是否进行此操作？",
    prompt: "提示",
    productList: "商品",
    isOnline: "已上架",
    isOutline: "待上架",
    isOuted: "已下架",
    selectTip: "请选择要上架下架删除的内容"
  },
  product: {
    search: "商品搜索：",
    keywordsPlaceholder: "请输入商品名称、关键字",
    status: "状态：",
    pleaseSelect: "请选择",
    query: "查询",
    reset: "重置",
    addProduct: "新增商品",
    batchOnline: "批量上架",
    batchOffline: "批量下架",
    batchDelete: "批量删除",
    productImage: "商品图",
    productName: "商品名称",
    productPrice: "商品价格(Rp)",
    cashbackRate: "商品返现率",
    estimatedCashback: "预计返现金额（Rp）",
    productLink: "商品链接",
    shareCount: "商品分享数量",
    soldCount: "已售数量",
    cashbackAmount: "已返现金额（Rp）",
    addTime: "商品添加时间",
    action: "操作",
    offline: "下架",
    online: "上架",
    edit: "编辑",
    delete: "删除",
    isHot: "热门",
    isBenefit: "高返现",
    isTikTok: "TikTok",
    addDialogTitle: "新增商品",
    enterProductLink: "请输入商品链接",
    fetchProductInfo: "拉取商品信息",
    enterProductName: "请输入商品名称",
    productPrice: "商品价格",
    enterProductPrice: "请输入商品价格",
    // cashbackRate: "返现率",
    usercashbackRate: "用户返现率",
    enterCashbackRate: "请输入返现率",
    // estimatedCashback: "预计返现金额",
    enterCashbackAmount: "请输入返现金额",
    isOnline: "是否上架：",
    yes: "是",
    no: "否",
    confirm: "确定",
    cancel: "取消",
    all: "全部",
    fetchProductFailed: "拉取商品信息失败",
    isOnIndex: "是否显示在首页",
    isOnline: "已上架",
    isOutline: "待上架",
    isOuted: "已下架"
  },
  operations: {
    withdrawal: {
      walletWithdrawal: "电子钱包提现",
      bankWithdrawal: "银行卡提现",
      applicant: "申请人",
      applicationTime: "申请时间",
      electronicWallet: "电子钱包",
      bankName: "银行名称",
      applicationId: "申请ID",
      applicantName: "申请人",
      withdrawalAmount: "提现金额",
      serviceFee: "手续费",
      actualAmount: "实际到账金额",
      walletCode: "电子钱包",
      walletAccount: "账户",
      bankCardNumber: "银行卡号",
      name: "姓名",
      phoneNumber: "手机号",
      withdrawalCount: "历史提现次数",
      auditResult: "审核结果",
      rejectReason: "拒绝原因",
      approve: "审核通过",
      reject: "审核拒绝",
      rejectReview: "审核拒绝",
      exportExcel: "导出excel表",
      transferTime: "转账时间",
      transferResult: "转账结果",
      remark: "备注",
      attachment: "附件",
      operator: "操作人",
      withdrawalStatus: "提现状态",
      ShopeePay: "ShopeePay",
      DANA: "DANA",
      OVO: "OVO",
      Gopay: "Gopay",
      unapproved: "未通过",
      underReview: "审核中",
      reviewed: "已审核",
      paid: "已打款"
    }
  },
  order: {
    search: {
      orderNo: "订单编号",
      productTitle: "商品名称",
      status: "状态",
      all: "全部",
      query: "查询",
      reset: "重置",
      exportExcel: "导出excel表",
      serialNumber: "序号",
      productImage: "商品图片",
      orderId: "订单ID",
      productName: "商品名称",
      payCount: "购买数量",
      actualCommission: "商品价格（Rp）",
      payPrice: "订单金额（Rp）",
      commissionRate: "商品返现率",
      estimatedCommission: "预计返现金额（Rp）",
      contentId: "电商平台",
      statusLabel: "订单状态",
      unknown: "未知",
      ordered: "已下单",
      settled: "已结算",
      refunded: "已退款",
      frozen: "冻结",
      deducted: "已扣除",
      totalPrice: "金额",
      userCashBackRate: "用户返现率",
      creatTime: "下单时间"
    }
  },
  user: {
    center: {
      nickname: "昵称",
      phone: "手机号",
      userLevel: "用户等级",
      query: "查询",
      reset: "重置",
      serialNumber: "序号",
      avatar: "头像",
      tiktokAccount: "TikTok昵称",
      tiktokId: "TikTok ID",
      whatsApp: "WhatsApp号",
      registerTime: "注册时间",
      lastLoginTime: "上次登陆时间",
      orderCount: "用户下单数量",
      orderFinishCount: "订单完成数量",
      isAgent: "是否是代理",
      isPartner: "是否是合作伙伴",
      userLevelLabel: "用户等级",
      inviter: "邀请人",
      userTags: "用户标签"
    },
    levelUpgrade: {
      title: "等级升级订单管理",
      orderNo: "订单号",
      userId: "用户ID",
      upgradeInfo: "升级信息",
      upgradeFee: "升级费用",
      paymentMethod: "支付方式",
      orderStatus: "订单状态",
      createTime: "创建时间",
      payTime: "支付时间",
      operation: "操作",
      enterOrderNo: "请输入订单号",
      selectStatus: "请选择状态",
      pending: "待支付",
      paid: "已支付",
      cancelled: "已取消",
      refunded: "已退款",
      cancelOrder: "取消订单",
      viewDetail: "查看详情",
      orderDetail: "订单详情",
      fromLevel: "原等级",
      toLevel: "目标等级",
      remark: "备注",
      noRemark: "无",
      unpaid: "未支付",
      confirmCancel: "确定要取消这个订单吗？",
      cancelSuccess: "订单已取消",
      cancelFailed: "取消订单失败",
      getListFailed: "获取订单列表失败",
      balancePayment: "余额支付",
      unknownLevel: "未知等级",
      unknownStatus: "未知状态",
      changeWarning: "请勿频繁更改，以免计算产生混乱！",
      deductExperience: "扣除经验",
      levelNames: {
        1: "普通用户",
        2: "银牌用户",
        3: "金牌用户",
        4: "钻石用户",
        5: "王者用户",
        6: "总团用户"
      }
    },
    grade: {
      title: "用户等级",
      levelName: "等级名称",
      experience: "经验",
      discount: "享受折扣",
      commissionRate: "佣金比例",
      upgradeType: "升级方式",
      upgradeFee: "升级费用",
      availableStatus: "开放状态",
      status: "状态",
      operation: "操作",
      available: "已开放",
      unavailable: "未开放",
      free: "免费",
      addUserLevel: "添加用户等级",
      levelIcon: "等级图标",
      enable: "开启",
      disable: "关闭",
      edit: "编辑",
      delete: "删除",
      deleteConfirm: "删除吗？删除会导致对应用户等级数据清空，请谨慎操作！",
      deleteSuccess: "删除成功",
      updateSuccess: "修改成功",
      hideConfirm: "该操作会导致对应用户等级隐藏，请谨慎操作",
      userTypes: {
        wechat: "微信用户",
        routine: "小程序用户",
        h5: "H5用户"
      },
      upgradeTypes: {
        0: "注册即可",
        1: "付费购买",
        2: "线下申请",
        3: "渠道合作"
      },
      form: {
        dialogTitle: "用户等级",
        levelNameLabel: "等级名称",
        levelNamePlaceholder: "请输入等级名称",
        gradeLabel: "等级",
        gradePlaceholder: "请输入等级",
        discountLabel: "享受折扣(%)",
        discountPlaceholder: "请输入享受折扣",
        experienceLabel: "经验",
        experiencePlaceholder: "请输入经验",
        iconLabel: "图标",
        cancel: "取 消",
        confirm: "确 定",
        editSuccess: "编辑成功",
        addSuccess: "添加成功",
        validation: {
          levelNameRequired: "请输入等级名称",
          gradeRequired: "请输入等级",
          gradeNumber: "等级必须为数字值",
          discountRequired: "请输入折扣",
          experienceRequired: "请输入经验",
          experienceNumber: "经验必须为数字值",
          iconRequired: "请上传图标",
          imageRequired: "请上传用户背景"
        }
      }
    }
  },
  financial: {
    detail: {
      title: "财务明细",
      purchaseDetail: "会员购买明细",
      tradeDetail: "交易明细",
      rechargeType: "产品名称",
      transactionTime: "交易时间",
      paymentMethod: "付款方式",
      electronicWallet: "电子钱包",
      bankName: "付款银行",
      serialNumber: "序号",
      paymentTime: "交易时间",
      paymentNo: "付款流水号",
      actualPaymentAmount: "实际付款金额",
      institutionNumber: "机构编号",
      paymentAccount: "付款账号",
      mobile: "手机号",
      payee: "收款人",
      payeeAccount: "收款账号",
      tradeNo: "交易流水号",
      tradeType: "交易类型",
      tradeAmount: "交易金额（Rp）",
      userNickname: "用户昵称",
      tikTokAccount: "TickTok",
      whatsApp: "WhatsApp号",
      channel: "渠道",
      orderNo: "订单编号",
      bankTransfer: "银行转账",
      electronicWallet: "电子钱包",
      agentFee: "代理费",
      partnerFee: "合作伙伴费",
      exportExcel: "导出excel表",
      ShopeePay: "ShopeePay",
      DANA: "DANA",
      OVO: "OVO",
      Gopay: "Gopay"
    },
    request: {
      walletWithdrawal: "电子钱包提现",
      bankWithdrawal: "银行卡提现",
      applicant: "申请人",
      applicationTime: "申请时间",
      electronicWallet: "电子钱包",
      bankName: "银行名称",
      serialNumber: "序号",
      applicationId: "申请ID",
      applicantName: "申请人",
      withdrawalAmount: "提现金额",
      serviceFee: "手续费",
      actualAmount: "实际到账金额",
      applicationTime: "申请时间",
      walletCode: "电子钱包",
      walletAccount: "账户",
      bankCardNumber: "银行卡号",
      name: "姓名",
      phoneNumber: "手机号",
      action: "操作",
      transferComplete: "转账完成",
      attachment: "附件",
      remark: "备注",
      confirm: "确定",
      cancel: "取消",
      exportExcel: "导出excel表"
    },
    history: {
      walletWithdrawal: "电子钱包提现",
      bankWithdrawal: "银行卡提现",
      applicant: "申请人",
      applicationTime: "申请时间",
      electronicWallet: "电子钱包",
      bankName: "银行名称",
      serialNumber: "序号",
      applicationId: "申请ID",
      applicantName: "申请人",
      withdrawalAmount: "提现金额",
      serviceFee: "手续费",
      actualAmount: "实际到账金额",
      applicationTime: "申请时间",
      walletCode: "电子钱包",
      walletAccount: "账户",
      bankCardNumber: "银行卡号",
      name: "姓名",
      phoneNumber: "手机号",
      transferTime: "转账时间",
      transferResult: "转账结果",
      remark: "备注",
      attachment: "附件",
      operator: "操作人",
      exportExcel: "导出excel表",
      status: "提现状态"
    }
  },
  parameter: {
    rewardRules: {
      title: "奖励规则设置",
      rewardTemplateName: "模板名称",
      rewardTemplateId: "奖励规则模板ID",
      directInviteReward: "直接邀请每人奖励",
      secondLevelInviteReward: "间接邀请（二级）每人可获得（Rp）",
      thirdLevelInviteReward: "间接邀请（三级）每人可获得（Rp）",
      goldRewardPer10: "每发展10名金牌奖励（Rp）",
      diamondRewardPer10: "每发展10名钻石奖励（Rp）",
      operation: "操作",
      edit: "编辑",
      editTitle: "编辑奖励规则设置",
      directAgentLabel: "直接邀请人是代理",
      directPartnerLabel: "直接邀请人是合作伙伴",
      indirectAgent2LevelLabel: "间接邀请（二级是代理）每人可获得（Rp）",
      indirectPartner2LevelLabel: "间接邀请（二级是合作伙伴）每人可获得（Rp）",
      indirectAgent3LevelLabel: "间接邀请（三级是代理）每人可获得（Rp）",
      indirectPartner3LevelLabel: "间接邀请（三级是合作伙伴）每人可获得（Rp）"
    },
    withdrawalFee: {
      title: "提现手续费设置",
      feeTemplateId: "费率模板ID",
      minWithdrawAmount: "最低提现金额（Rp）",
      maxWithdrawAmount: "最高提现金额（Rp）",
      withdrawFeeRate: "手续费费率（%）",
      operation: "操作",
      edit: "编辑",
      addTitle: "新增费率规则",
      editTitle: "编辑费率规则",
      placeholder: {
        couponId: "请输入代金券ID",
        minWithdrawAmount: "请输入最低提现金额",
        maxWithdrawAmount: "请输入最高提现金额",
        withdrawFeeRate: "请输入手续费费率"
      }
    },
    membershipFee: {
      title: "会员升级费设置",
      feeTemplateId: "费率模板ID",
      agentFee: "代理费（Rp）",
      partnerFee: "合作伙伴费（Rp）",
      operation: "操作",
      edit: "编辑",
      addTitle: "新增会员升级费",
      editTitle: "编辑会员升级费",
      placeholder: {
        agentFee: "请输入代理费",
        partnerFee: "请输入合作伙伴费"
      }
    },
    shoppingCashbackRules: {
      directCashbackRate: "直接可获得返佣比例（%）",
      secondLevelCashbackRate: "二级返佣比例（%）",
      thirdLevelCashbackRate: "三级返佣比例（%）",
      normalUserRule: "普通用户返佣规则",
      agentTeamRule: "代理团队返佣规则",
      partnerTeamRule: "合作伙伴团队返佣规则"
    },
    referralRewardConfig: {
      title: "拉新奖励配置",
      rewardTemplateId: "奖励规则模版ID",
      rewardTemplateName: "模版名称",
      referralCount: "拉新数",
      firstOrderCount: "首单数",
      rewardAmount: "奖励金（Rp）",
      rewardRuleZh: "奖励规则（中文）",
      rewardRuleEn: "奖励规则（英文）",
      rewardRuleId: "奖励规则（印尼）",
      operation: "操作",
      edit: "编辑",
      editTitle: "编辑拉新奖励配置",
      basicConfig: "基础配置",
      validation: {
        referralCountMin: "拉新数必须大于等于0",
        firstOrderCountMin: "首单数必须大于等于0",
        firstOrderCountMax: "首单数必须小于拉新数",
        rewardAmountMin: "奖励金必须大于等于0"
      }
    }
  },
  admin: {
    system: {
      role: {
        roleName: "角色昵称",
        roleId: "角色编号",
        status: "状态",
        createTime: "创建时间",
        updateTime: "更新时间",
        operation: "操作",
        addRole: "新增角色",
        editRole: "编辑角色",
        deleteRole: "删除角色",
        confirmDelete: "确认删除当前数据",
        deleteSuccess: "删除数据成功",
        createIdentity: "创建身份",
        editIdentity: "编辑身份",
        roleForm: {
          roleNameLabel: "角色名称",
          roleNamePlaceholder: "身份名称",
          statusLabel: "状态",
          menuPermissions: "菜单权限",
          expandCollapse: "展开/折叠",
          selectAll: "全选/全不选",
          parentChildLink: "父子联动",
          confirm: "确定",
          update: "更新",
          cancel: "取消"
        }
      },
      admin: {
        role: "身份",
        status: "状态",
        realName: "姓名或账号",
        id: "ID",
        account: "账号",
        phone: "手机号",
        lastTime: "最后登录时间",
        lastIp: "最后登录IP",
        isSms: "是否接收短信",
        isDel: "删除标记",
        operation: "操作",
        addAdmin: "添加管理员",
        edit: "编辑",
        delete: "删除",
        createIdentity: "创建身份",
        editIdentity: "编辑身份",
        pleaseAddPhone: "请先为管理员添加手机号!",
        confirmDelete: "确认删除当前数据",
        deleteSuccess: "删除数据成功",
        account: "账号",
        pwd: "密码",
        repwd: "确认密码",
        realName: "姓名",
        roles: "角色",
        phone: "手机号",
        pleaseAddPhone: "请先为管理员添加手机号!",
        validatePhone: {
          required: "请填写手机号",
          formatError: "手机号格式不正确!"
        },
        validatePass: {
          required: "请再次输入密码",
          notMatch: "两次输入密码不一致!"
        },
        message: {
          createSuccess: "创建管理员成功",
          updateSuccess: "更新管理员成功"
        },
        validateAccount: {
          required: "请填写管理员账号"
        },
        validatePassword: {
          required: "请填写管理员密码"
        },
        validateConfirmPassword: {
          required: "请确认密码"
        },
        validateRealName: {
          required: "请填写管理员姓名"
        },
        validateRoles: {
          required: "请选择管理员角色"
        },
        validatePassword: {
          required: "请填写管理员密码",
          lengthError: "密码长度需为6-20个字符"
        },
        validateConfirmPassword: {
          required: "请确认密码"
        },
        validatePass: {
          notMatch: "两次输入密码不一致"
        }
      }
    }
  },
  permissionRules: {
    menuName: "菜单名称",
    status: "状态",
    select: "请选择",
    add: "新增",
    expandCollapse: "展开/折叠",
    actions: {
      edit: "修改",
      add: "新增",
      delete: "删除"
    },
    table: {
      menuName: "菜单名称",
      icon: "图标",
      sort: "排序",
      perm: "权限标识",
      component: "组件路径",
      status: "状态",
      createTime: "创建时间",
      type: "类型"
    },
    menuType: {
      directory: "目录",
      menu: "菜单",
      button: "按钮"
    },
    form: {
      parentMenu: "上级菜单",
      menuType: "菜单类型",
      menuIcon: "菜单图标",
      menuName: "菜单名称",
      sort: "显示排序",
      component: "组件路径",
      componentTip:
        "访问的组件路径，如：`system/user/index`，默认在`views`目录下",
      perm: "权限字符",
      permTip:
        '控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi("system: user: list")`)',
      showStatus: "显示状态",
      showStatusTip: "选择隐藏则路由将不会出现在侧边栏，但仍然可以访问",
      enterMenuName: "请输入菜单名称",
      enterComponent: "请输入组件路径",
      enterPerm: "请输入权限标识",
      selectIcon: "请选择菜单图标",
      selectParentMenu: "选择上级菜单",
      sortRequired: "显示排序不能为空"
    }
  },
  affiliateProducts: {
    title: "联盟选品",
    keywords: "关键词：",
    keywordsPlaceholder: "请输入商品关键词",
    priceRange: "价格范围：",
    minPrice: "最低价格",
    maxPrice: "最高价格",
    commissionRange: "佣金率范围：",
    minCommission: "最低佣金率(%)",
    maxCommission: "最高佣金率(%)",
    sort: "排序：",
    sortCommissionRate: "佣金率",
    sortCommission: "佣金金额",
    sortPrice: "商品价格",
    sortSales: "销量",
    sortDesc: "降序",
    sortAsc: "升序",
    query: "查询",
    reset: "重置",
    refresh: "刷新",
    listTitle: "联盟选品列表",
    batchImport: "批量入库",
    batchImporting: "批量入库中...",
    batchDelete: "批量删除",
    batchDeleting: "批量删除中...",
    emptyTip: "点击查询按钮开始搜索商品",
    serialNumber: "序号",
    productImage: "商品图片",
    productTitle: "商品标题",
    shop: "店铺",
    originalPrice: "原价",
    salesPrice: "售价",
    commissionRate: "佣金率",
    commissionAmount: "佣金金额",
    unitsSold: "销量",
    inventoryStatus: "库存状态",
    hasInventory: "有库存",
    noInventory: "无库存",
    saleRegion: "销售区域",
    importStatus: "导入状态",
    imported: "已导入",
    notImported: "未导入",
    action: "操作",
    import: "商品入库",
    importing: "入库中...",
    imported: "已入库",
    delete: "删除",
    deleting: "删除中...",
    prevPage: "上一页",
    nextPage: "下一页",
    pageSize: "每页显示：",
    totalCount: "共 {count} 个商品",
    importSingle: "导入单个商品",
    importBatch: "批量导入商品",
    selectedCount: "选中商品数量：",
    brandAutoDetect: "品牌信息将自动从TikTok商品数据中识别，无需手动选择",
    confirmImport: "确定导入",
    cancel: "取消",
    deleteConfirm: "确定要删除这个商品吗？",
    batchDeleteConfirm: "确定要批量删除 {count} 个商品吗？",
    deleteSuccess: "删除成功",
    batchDeleteSuccess: "批量删除成功",
    importSuccess: "商品导入成功！",
    batchImportSuccess: "批量导入完成！成功导入 {count} 个商品",
    importExists: "商品已存在，无需重复导入",
    batchImportExists: "所有商品都已存在，无需重复导入",
    batchImportPartial: "批量导入部分成功！成功：{success}，失败：{failed}，跳过：{skipped}",
    batchImportMixed: "批量导入完成！成功：{success}，跳过（已存在）：{skipped}",
    importFailed: "商品导入失败！失败原因：{reason}",
    batchImportFailed: "批量导入失败！失败：{failed}，跳过：{skipped}",
    selectFirst: "请先选择要导入的商品",
    selectDeleteFirst: "请先选择要删除的商品",
    searchFirst: "请先点击查询按钮",
    noResults: "未找到符合条件的商品"
  },
  chainTransferRecord: {
    title: "转链记录",
    keyword: "关键词",
    brandName: "品牌名称",
    query: "查询",
    reset: "重置",
    exportExcel: "导出excel表",
    serialNumber: "序号",
    nickname: "昵称",
    tiktokId: "TikTok ID",
    originalLink: "原始链接",
    rebateLink: "转链后的返利链接",
    operationTime: "操作时间",
    linkSource: "链接来源",
    productId: "商品ID",
    productName: "商品名称",
    productPrice: "商品价格",
    productCashbackRate: "商品返现率",
    userCashbackRate: "用户返现率"
  },
  message: {
    hello: "你好",
    userNotice: "用户通知",
    userDetails: {
      balance: "余额",
      allOrderCount: "总计订单",
      allConsumeCount: "总消费金额",
      integralCount: "积分",
      mothOrderCount: "本月订单",
      mothConsumeCount: "本月消费金额",
      consumeRecord: "消费记录",
      integralDetail: "积分明细",
      signInRecord: "签到记录",
      coupons: "持有优惠券",
      balanceChange: "余额变动",
      friendRelation: "好友关系",
      sourceOrPurpose: "来源/用途",
      integralChange: "积分变化",
      balanceAfterChange: "变化后积分",
      date: "日期",
      remark: "备注",
      orderId: "订单ID",
      receiver: "收货人",
      goodsNum: "商品数量",
      goodsTotalPrice: "商品总价",
      payPrice: "实付金额",
      payTime: "交易完成时间",
      action: "动作",
      getIntegral: "获得积分",
      signTime: "签到时间",
      couponName: "优惠券名称",
      faceValue: "面值",
      validity: "有效期",
      minPrice: "最低消费额",
      exchangeTime: "兑换时间",
      changeAmount: "变动金额",
      afterChange: "变动后",
      type: "类型",
      createTime: "创建时间",
      id: "ID",
      nickname: "昵称",
      level: "等级",
      joinTime: "加入时间"
    }
  }
};
