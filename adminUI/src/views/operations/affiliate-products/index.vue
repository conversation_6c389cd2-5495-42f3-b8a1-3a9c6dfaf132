<template>
  <div class="divBox relative">
    <!-- 搜索表单 -->
    <el-card class="box-card">
      <el-form :model="searchForm" :rules="searchRules" ref="searchFormRef" inline size="small">
        <el-form-item :label="$t('affiliateProducts.keywords')">
          <el-input
            v-model="searchForm.keywords"
            :placeholder="$t('affiliateProducts.keywordsPlaceholder')"
            clearable
            style="width: 200px;"
          ></el-input>
        </el-form-item>

        <el-form-item :label="$t('affiliateProducts.priceRange')">
          <el-input
            v-model="searchForm.priceMin"
            :placeholder="$t('affiliateProducts.minPrice')"
            style="width: 120px;"
          ></el-input>
          <span style="margin: 0 8px;">-</span>
          <el-input
            v-model="searchForm.priceMax"
            :placeholder="$t('affiliateProducts.maxPrice')"
            style="width: 120px;"
          ></el-input>
        </el-form-item>

        <el-form-item :label="$t('affiliateProducts.commissionRange')">
          <el-input
            v-model="searchForm.commissionMin"
            :placeholder="$t('affiliateProducts.minCommission')"
            style="width: 120px;"
          ></el-input>
          <span style="margin: 0 8px;">-</span>
          <el-input
            v-model="searchForm.commissionMax"
            :placeholder="$t('affiliateProducts.maxCommission')"
            style="width: 120px;"
          ></el-input>
        </el-form-item>

        <el-form-item :label="$t('affiliateProducts.sort')">
          <el-select v-model="searchForm.sortField" style="width: 140px;">
            <el-option :label="$t('affiliateProducts.sortCommissionRate')" value="commission_rate"></el-option>
            <el-option :label="$t('affiliateProducts.sortCommission')" value="commission"></el-option>
            <el-option :label="$t('affiliateProducts.sortPrice')" value="product_sales_price"></el-option>
            <el-option :label="$t('affiliateProducts.sortSales')" value="units_sold"></el-option>
          </el-select>
          <el-select v-model="searchForm.sortOrder" style="width: 80px; margin-left: 8px;">
            <el-option :label="$t('affiliateProducts.sortDesc')" value="DESC"></el-option>
            <el-option :label="$t('affiliateProducts.sortAsc')" value="ASC"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">{{ $t('affiliateProducts.query') }}</el-button>
          <el-button @click="handleReset">{{ $t('affiliateProducts.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 产品列表 -->
    <el-card class="box-card" style="margin-top: 12px;">
      <div slot="header" class="clearfix">
        <span>{{ $t('affiliateProducts.listTitle') }}</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="handleRefresh"
        >{{ $t('affiliateProducts.refresh') }}</el-button>
      </div>

      <!-- 批量操作按钮 -->
      <div v-if="hasSearched && tableData.length > 0" style="margin-bottom: 15px;">
        <el-button
          type="primary"
          size="small"
          @click="handleBatchImport"
          :disabled="selectedProducts.length === 0 || batchImporting"
        >
          {{ batchImporting ? $t('affiliateProducts.batchImporting') : `${$t('affiliateProducts.batchImport')} (${selectedProducts.length})` }}
        </el-button>
        <el-button
          type="danger"
          size="small"
          @click="handleBatchDelete"
          :disabled="selectedProducts.length === 0 || batchDeleting"
        >
          {{ batchDeleting ? $t('affiliateProducts.batchDeleting') : `${$t('affiliateProducts.batchDelete')} (${selectedProducts.length})` }}
        </el-button>
      </div>

      <!-- 未搜索时的提示 -->
      <div v-if="!hasSearched && tableData.length === 0" class="empty-tip">
        <el-empty :description="$t('affiliateProducts.emptyTip')"></el-empty>
      </div>

      <el-table
        ref="productTable"
        v-loading="loading"
        :data="tableData"
        size="small"
        :header-cell-style="{ fontWeight: 'bold' }"
        v-show="hasSearched"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column type="index" :label="$t('affiliateProducts.serialNumber')" width="60"></el-table-column>

        <el-table-column :label="$t('affiliateProducts.productImage')" width="100">
          <template slot-scope="scope">
            <el-image
              :src="scope.row.mainImageUrl"
              fit="cover"
              style="width: 60px; height: 60px; border-radius: 4px;"
              :preview-src-list="[scope.row.mainImageUrl]"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.productTitle')" min-width="200">
          <template slot-scope="scope">
            <el-link :href="scope.row.detailLink" target="_blank" type="primary">
              {{ scope.row.title }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.shop')" width="120">
          <template slot-scope="scope">
            {{ scope.row.shop ? scope.row.shop.name : '-' }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.originalPrice')" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.originalPrice">
              {{ formatPrice(scope.row.originalPrice) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.salesPrice')" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.salesPrice">
              {{ formatPrice(scope.row.salesPrice) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.commissionRate')" width="80">
          <template slot-scope="scope">
            <span v-if="scope.row.commission">
              {{ formatCommissionRate(scope.row.commission.rate) }}%
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.commissionAmount')" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.commission">
              {{ scope.row.commission.amount }} {{ scope.row.commission.currency }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.unitsSold')" width="80">
          <template slot-scope="scope">
            {{ scope.row.unitsSold || 0 }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.inventoryStatus')" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.hasInventory ? 'success' : 'danger'" size="mini">
              {{ scope.row.hasInventory ? $t('affiliateProducts.hasInventory') : $t('affiliateProducts.noInventory') }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.saleRegion')" width="80">
          <template slot-scope="scope">
            {{ scope.row.saleRegion || '-' }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.importStatus')" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isImported ? 'success' : 'info'" size="mini">
              {{ scope.row.isImported ? $t('affiliateProducts.imported') : $t('affiliateProducts.notImported') }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.action')" width="180" fixed="right">
          <template slot-scope="scope">
            <el-button
              :type="scope.row.isImported ? 'success' : 'primary'"
              size="mini"
              @click="handleImportProduct(scope.row)"
              :disabled="scope.row.importing || scope.row.isImported"
            >
              {{ scope.row.importing ? $t('affiliateProducts.importing') : (scope.row.isImported ? $t('affiliateProducts.imported') : $t('affiliateProducts.import')) }}
            </el-button>
            <el-button
              type="danger"
              size="mini"
              @click="handleDeleteProduct(scope.row)"
              :disabled="scope.row.deleting"
            >
              {{ scope.row.deleting ? $t('affiliateProducts.deleting') : $t('affiliateProducts.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div v-if="hasSearched" class="pagination-container" style="margin-top: 20px; text-align: center;">
        <el-button
          :disabled="!hasPrevPage"
          @click="handlePrevPage"
          size="small"
        >{{ $t('affiliateProducts.prevPage') }}</el-button>
        <el-button
          :disabled="!hasNextPage"
          @click="handleNextPage"
          size="small"
        >{{ $t('affiliateProducts.nextPage') }}</el-button>
        <span style="margin-left: 20px;">
          {{ $t('affiliateProducts.pageSize') }}
          <el-select v-model="searchForm.pageSize" size="mini" style="width: 80px;" @change="handleSearch">
            <el-option label="10" :value="10"></el-option>
            <el-option label="20" :value="20"></el-option>
            <el-option label="50" :value="50"></el-option>
          </el-select>
        </span>
        <span style="margin-left: 20px;">
          {{ $t('affiliateProducts.totalCount', { count: totalCount }) }}
        </span>
      </div>
    </el-card>

    <!-- 导入确认对话框 -->
    <el-dialog
      :title="currentImportProduct ? $t('affiliateProducts.importSingle') : $t('affiliateProducts.importBatch')"
      :visible.sync="importDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <div v-if="currentImportProduct">
        <p><strong>{{ $t('affiliateProducts.productTitle') }}：</strong>{{ currentImportProduct.title }}</p>
        <p><strong>{{ $t('product.productId') }}：</strong>{{ currentImportProduct.id }}</p>
      </div>
      <div v-else>
        <p><strong>{{ $t('affiliateProducts.selectedCount') }}</strong>{{ selectedProducts.length }}</p>
      </div>
      <div style="margin: 20px 0; padding: 15px; background-color: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px;">
        <i class="el-icon-info" style="color: #409eff; margin-right: 8px;"></i>
        <span style="color: #409eff;">{{ $t('affiliateProducts.brandAutoDetect') }}</span>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">{{ $t('affiliateProducts.cancel') }}</el-button>
        <el-button type="primary" @click="confirmImport">{{ $t('affiliateProducts.confirmImport') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { searchAffiliateProducts } from '@/api/tiktok'
import { importAffiliateProducts, deleteAffiliateProducts } from '@/api/affiliate-products'

export default {
  name: 'AffiliateProducts',
  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        keywords: '',
        priceMin: '',
        priceMax: '',
        commissionMin: '',
        commissionMax: '',
        sortField: 'commission_rate',
        sortOrder: 'DESC',
        pageSize: 20
      },
      searchRules: {
        // 移除关键词必填验证
      },
      currentCursor: '',
      nextPageToken: '',
      prevPageTokens: [], // 存储前面页面的token，用于返回上一页
      totalCount: 0,
      hasNextPage: false,
      hasPrevPage: false,
      hasSearched: false, // 标记是否已经搜索过
      selectedProducts: [], // 选中的商品列表
      batchImporting: false, // 批量入库状态
      batchDeleting: false, // 批量删除状态
      importDialogVisible: false, // 入库确认对话框
      currentImportProduct: null // 当前导入的商品（单个导入时使用）
    }
  },
  mounted() {
    // 页面加载时不自动搜索，等待用户点击查询按钮
  },
  methods: {
    // 搜索
    handleSearch() {
      this.currentCursor = ''
      this.nextPageToken = ''
      this.prevPageTokens = []
      this.hasPrevPage = false
      this.hasSearched = true
      this.loadData()
    },

    // 重置
    handleReset() {
      this.searchForm = {
        keywords: '',
        priceMin: '',
        priceMax: '',
        commissionMin: '',
        commissionMax: '',
        sortField: 'commission_rate',
        sortOrder: 'DESC',
        pageSize: 20
      }
      // 重置表单（无需验证状态重置）
      // 清空表格数据
      this.tableData = []
      this.hasSearched = false
      this.totalCount = 0
      this.hasNextPage = false
      this.hasPrevPage = false
      this.currentCursor = ''
      this.nextPageToken = ''
      this.prevPageTokens = []
    },

    // 刷新
    handleRefresh() {
      if (this.hasSearched) {
        this.loadData()
      } else {
        this.$message.warning(this.$t('affiliateProducts.searchFirst'))
      }
    },

    // 下一页
    handleNextPage() {
      if (this.hasNextPage && this.nextPageToken) {
        this.prevPageTokens.push(this.currentCursor)
        this.currentCursor = this.nextPageToken
        this.hasPrevPage = true
        this.loadData()
      }
    },

    // 上一页
    handlePrevPage() {
      if (this.hasPrevPage && this.prevPageTokens.length > 0) {
        this.currentCursor = this.prevPageTokens.pop()
        if (this.prevPageTokens.length === 0) {
          this.hasPrevPage = false
        }
        this.loadData()
      }
    },

    // 加载数据
    loadData() {
      this.loading = true

      // 构建请求参数 - 与V202405 AffiliateProductSearchRequest保持完全一致
      const params = {
        // 基础分页和排序参数
        pageSize: this.searchForm.pageSize,
        cursor: this.currentCursor,
        sortField: this.searchForm.sortField,
        sortOrder: this.searchForm.sortOrder,

        // 搜索过滤参数（按V202405 API字段名）
        titleKeywords: this.searchForm.keywords ? [this.searchForm.keywords] : null,
        salesPriceMin: this.searchForm.priceMin ? parseFloat(this.searchForm.priceMin) : null,
        salesPriceMax: this.searchForm.priceMax ? parseFloat(this.searchForm.priceMax) : null,
        commissionRateMin: this.searchForm.commissionMin ? parseFloat(this.searchForm.commissionMin) : null,
        commissionRateMax: this.searchForm.commissionMax ? parseFloat(this.searchForm.commissionMax) : null
      }

      searchAffiliateProducts(params)
        .then(res => {
          this.tableData = res.products || []
          this.nextPageToken = res.nextPageToken || ''
          this.totalCount = res.totalCount || 0
          this.hasNextPage = !!this.nextPageToken

          if (this.tableData.length === 0) {
            this.$message.info(this.$t('affiliateProducts.noResults'))
          }
        })
        .catch(() => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 格式化价格
    formatPrice(priceObj) {
      if (!priceObj) return '-'
      const min = priceObj.minimumAmount
      const max = priceObj.maximumAmount
      const currency = priceObj.currency

      if (min === max) {
        return `${min} ${currency}`
      } else {
        return `${min} - ${max} ${currency}`
      }
    },

    // 格式化佣金率（基点转百分比）
    formatCommissionRate(rate) {
      if (!rate) return '0'
      return (rate / 100).toFixed(2)
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedProducts = selection
    },

    // 单个商品导入
    handleImportProduct(product) {
      // 设置当前操作的商品
      this.currentImportProduct = product
      this.importDialogVisible = true
    },

    // 单个商品删除
    handleDeleteProduct(product) {
      this.$confirm(this.$t('affiliateProducts.deleteConfirm'), this.$t('common.deleteConfirm'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      })
      .then(() => {
        product.deleting = true

        deleteAffiliateProducts([product.id])
        .then(() => {
          this.$message.success(this.$t('affiliateProducts.deleteSuccess'))
        })
        .catch(() => {
          // 错误消息已在响应拦截器中处理
        })
        .finally(() => {
          product.deleting = false
        })
      })
      .catch(() => {
        // 用户取消删除，不需要处理
      })
    },

    // 批量导入
    handleBatchImport() {
      if (this.selectedProducts.length === 0) {
        this.$message.warning(this.$t('affiliateProducts.selectFirst'))
        return
      }

      // 设置批量导入模式
      this.currentImportProduct = null
      this.importDialogVisible = true
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedProducts.length === 0) {
        this.$message.warning(this.$t('affiliateProducts.selectDeleteFirst'))
        return
      }

      this.$confirm(this.$t('affiliateProducts.batchDeleteConfirm', { count: this.selectedProducts.length }), this.$t('affiliateProducts.batchDelete'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      })
      .then(() => {
        this.batchDeleting = true

        const productIds = this.selectedProducts.map(p => p.id)
        deleteAffiliateProducts(productIds)
        .then(() => {
          this.$message.success(this.$t('affiliateProducts.batchDeleteSuccess'))
          // 清空选择
          this.$refs.productTable.clearSelection()
        })
        .catch(() => {
          // 错误消息已在响应拦截器中处理
        })
        .finally(() => {
          this.batchDeleting = false
        })
      })
      .catch(() => {
        // 用户取消删除，不需要处理
      })
    },



    // 确认导入
    confirmImport() {
      if (this.currentImportProduct) {
        // 单个商品导入
        this.currentImportProduct.importing = true
        this.importDialogVisible = false

        importAffiliateProducts({
          productIds: [this.currentImportProduct.id],
          operationUser: this.$store.getters.name || 'admin'
        })
        .then(data => {
          // 根据导入结果显示不同的消息
          if (data.failedCount === 0) {
            if (data.skippedCount > 0) {
              this.$message.warning(this.$t('affiliateProducts.importExists'))
            } else {
              this.$message.success(this.$t('affiliateProducts.importSuccess'))
            }
          } else {
            this.$message.error(this.$t('affiliateProducts.importFailed', {
              reason: data.failedProducts && data.failedProducts.length > 0 ? data.failedProducts[0].errorMessage : this.$t('common.unknownError')
            }))
          }
        })
        .catch(() => {
          // 错误消息已在响应拦截器中处理
        })
        .finally(() => {
          this.currentImportProduct.importing = false
        })
      } else {
        // 批量导入
        this.batchImporting = true
        this.importDialogVisible = false

        const productIds = this.selectedProducts.map(p => p.id)
        importAffiliateProducts({
          productIds,
          operationUser: this.$store.getters.name || 'admin'
        })
        .then(data => {
          // 根据导入结果显示不同的消息
          if (data.failedCount === 0) {
            if (data.skippedCount > 0 && data.successCount === 0) {
              this.$message.warning(this.$t('affiliateProducts.batchImportExists'))
            } else if (data.skippedCount > 0) {
              this.$message.success(this.$t('affiliateProducts.batchImportMixed', {
                success: data.successCount,
                skipped: data.skippedCount
              }))
            } else {
              this.$message.success(this.$t('affiliateProducts.batchImportSuccess', { count: data.successCount }))
            }
          } else if (data.successCount > 0) {
            // 部分成功
            this.$message.warning(this.$t('affiliateProducts.batchImportPartial', {
              success: data.successCount,
              failed: data.failedCount,
              skipped: data.skippedCount
            }))
          } else {
            // 全部失败
            this.$message.error(this.$t('affiliateProducts.batchImportFailed', {
              failed: data.failedCount,
              skipped: data.skippedCount
            }))
          }

          // 清空选择
          this.$refs.productTable.clearSelection()
        })
        .catch(() => {
          // 错误消息已在响应拦截器中处理
        })
        .finally(() => {
          this.batchImporting = false
        })
      }
    },
  }
}
</script>

<style scoped>
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.empty-tip {
  padding: 40px 0;
  text-align: center;
}
</style>
